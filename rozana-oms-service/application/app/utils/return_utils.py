from typing import Optional


def validate_return_eligibility(order_item: dict, return_quantity: int) -> tuple[bool, Optional[str]]:
    if not order_item.get('is_returnable', True):
        return False, f"Item {order_item.get('sku')} is not returnable"
    
    if return_quantity <= 0:
        return False, "Return quantity must be greater than 0"
    
    if return_quantity > order_item.get('quantity', 0):
        return False, f"Return quantity ({return_quantity}) cannot exceed ordered quantity ({order_item.get('quantity', 0)})"
    
    return_window = order_item.get('return_window')
    if return_window and return_window > 0:
        pass
    
    return True, None


def calculate_return_refund_amount(return_items: list) -> float:
    total_refund = 0.0
    
    for item in return_items:
        quantity = item.get('quantity_returned', 0)
        sale_price = float(item.get('sale_price', 0))
        total_refund += quantity * sale_price
    
    return round(total_refund, 2)
