"""
Pydantic models for return API requests and responses
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class ReturnItemRequest(BaseModel):
    """Request model for individual return item"""
    sku: str = Field(..., description="SKU of the item to return")
    quantity: int = Field(..., gt=0, description="Quantity to return (must be > 0)")


class PartialReturnRequest(BaseModel):
    """Request model for partial order return"""
    order_id: str = Field(..., description="Order ID to return items from")
    items: List[ReturnItemRequest] = Field(..., min_items=1, description="List of items to return")
    return_reason: Optional[str] = Field(None, description="Reason for return")


class FullReturnRequest(BaseModel):
    """Request model for full order return"""
    order_id: str = Field(..., description="Order ID to return completely")
    return_reason: Optional[str] = Field(None, description="Reason for return")


class ReturnItemResponse(BaseModel):
    """Response model for return item details"""
    id: Optional[int] = None
    sku: str
    quantity_returned: int
    unit_price: float
    sale_price: float
    return_reason: Optional[str] = None
    status: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class ReturnResponse(BaseModel):
    """Response model for return details"""
    id: Optional[int] = None
    return_id: str
    order_id: str
    customer_id: str
    return_type: str  # "partial" or "full"
    return_reason: Optional[str] = None
    return_method: str = "api"
    status: str = "approved"
    total_refund_amount: float
    refund_status: str = "pending"
    approved_at: Optional[str] = None
    processed_at: Optional[str] = None
    completed_at: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    items: Optional[List[ReturnItemResponse]] = None


class ReturnProcessResponse(BaseModel):
    """Response model for return processing result"""
    success: bool
    message: str
    order_id: str
    return_id: str
    return_type: str
    returned_items: List[dict]
    order_status: int
    total_refund_amount: float


class ReturnListResponse(BaseModel):
    """Response model for list of returns"""
    success: bool = True
    returns: List[ReturnResponse]
    total_count: Optional[int] = None


class ReturnDetailsResponse(BaseModel):
    """Response model for single return details"""
    success: bool = True
    return_data: Optional[ReturnResponse] = None
    message: Optional[str] = None
