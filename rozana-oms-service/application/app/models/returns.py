"""
SQLAlchemy ORM Models for Return Management
These models define the return tracking database schema using SQLAlchemy ORM.
"""

from sqlalchemy import Column, Integer, String, DECIMAL, TIMESTAMP, ForeignKey, Text, Index
from sqlalchemy.orm import relationship
from app.models.common import CommonModel

class ReturnRequest(CommonModel):

    __tablename__ = "return_requests"

    id = Column(Integer, primary_key=True, index=True)
    return_id = Column(String(50), unique=True, nullable=False, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    customer_id = Column(String(50), nullable=False, index=True)
    return_type = Column(String(10), nullable=False)  
    return_reason = Column(Text, nullable=True) 
    return_method = Column(String(20), nullable=False, default='api')
    status = Column(String(20), nullable=False, default='approved', index=True) 
    total_refund_amount = Column(DECIMAL(10, 2), nullable=True)
    refund_status = Column(String(20), nullable=False, default='pending')
    approved_at = Column(TIMESTAMP, nullable=True)
    processed_at = Column(TIMESTAMP, nullable=True)
    completed_at = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)

    order = relationship("Order", backref="return_requests")
    return_items = relationship("ReturnItem", back_populates="return_request", cascade="all, delete-orphan")
    return_images = relationship("ReturnImage", back_populates="return_request", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ReturnRequest(id={self.id}, return_id='{self.return_id}', order_id={self.order_id}, status='{self.status}')>"

    __table_args__ = (
        Index('idx_return_requests_customer_status', 'customer_id', 'status'),
        Index('idx_return_requests_created_at', 'created_at'),
    )


class ReturnItem(CommonModel):
    __tablename__ = "return_items"

    id = Column(Integer, primary_key=True, index=True)
    return_request_id = Column(Integer, ForeignKey("return_requests.id"), nullable=False, index=True)
    order_item_id = Column(Integer, ForeignKey("order_items.id"), nullable=False, index=True)
    sku = Column(String(100), nullable=False, index=True)
    quantity_returned = Column(Integer, nullable=False)
    unit_price = Column(DECIMAL(10, 2), nullable=False)
    sale_price = Column(DECIMAL(10, 2), nullable=False)
    refund_amount = Column(DECIMAL(10, 2), nullable=True)
    return_reason = Column(Text, nullable=True)
    item_condition = Column(String(20), nullable=True) 
    condition_notes = Column(Text, nullable=True)

    return_request = relationship("ReturnRequest", back_populates="return_items")
    order_item = relationship("OrderItem", backref="return_items")
    return_images = relationship("ReturnImage", back_populates="return_item", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ReturnItem(id={self.id}, return_request_id={self.return_request_id}, sku='{self.sku}', quantity={self.quantity_returned})>"


class ReturnImage(CommonModel):
    __tablename__ = "return_images"

    id = Column(Integer, primary_key=True, index=True)
    return_request_id = Column(Integer, ForeignKey("return_requests.id"), nullable=False, index=True)
    image = Column(String(500), nullable=False)  # Simple image path

    return_request = relationship("ReturnRequest", back_populates="return_images")

    def __repr__(self):
        return f"<ReturnImage(id={self.id}, return_request_id={self.return_request_id}, image='{self.image}')>"
