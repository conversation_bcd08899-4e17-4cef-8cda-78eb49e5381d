"""
Return Service for handling return-related database operations
"""

import logging
from typing import List, Dict, Optional
from sqlalchemy import text
from app.connections.database import get_raw_transaction

logger = logging.getLogger(__name__)


class ReturnService:
    """Service class for return operations"""
    
    def get_return_by_id(self, return_id: str) -> Optional[Dict]:
        """
        Get return details by return_id
        
        Args:
            return_id: The return ID to fetch
            
        Returns:
            Dict containing return details or None if not found
        """
        try:
            with get_raw_transaction() as conn:
                get_return_sql = """
                    SELECT 
                        rr.id, rr.return_id, rr.order_id, rr.customer_id,
                        rr.return_type, rr.return_reason, rr.return_method,
                        rr.status, rr.total_refund_amount, rr.refund_status,
                        rr.approved_at, rr.processed_at, rr.completed_at,
                        rr.created_at, rr.updated_at,
                        o.order_id as order_reference
                    FROM return_requests rr
                    JOIN orders o ON rr.order_id = o.order_id
                    WHERE rr.return_id = :return_id
                """
                
                result = conn.execute(text(get_return_sql), {'return_id': return_id})
                return_row = result.fetchone()
                
                if not return_row:
                    return None
                
                return_data = {
                    'id': return_row.id,
                    'return_id': return_row.return_id,
                    'order_id': return_row.order_reference,
                    'customer_id': return_row.customer_id,
                    'return_type': return_row.return_type,
                    'return_reason': return_row.return_reason,
                    'return_method': return_row.return_method,
                    'status': return_row.status,
                    'total_refund_amount': float(return_row.total_refund_amount) if return_row.total_refund_amount else 0.0,
                    'refund_status': return_row.refund_status,
                    'approved_at': return_row.approved_at.isoformat() if return_row.approved_at else None,
                    'processed_at': return_row.processed_at.isoformat() if return_row.processed_at else None,
                    'completed_at': return_row.completed_at.isoformat() if return_row.completed_at else None,
                    'created_at': return_row.created_at.isoformat() if return_row.created_at else None,
                    'updated_at': return_row.updated_at.isoformat() if return_row.updated_at else None
                }
                
                # Get return items
                get_items_sql = """
                    SELECT 
                        ri.id, ri.sku, ri.quantity_returned, ri.unit_price,
                        ri.sale_price, ri.return_reason, ri.status,
                        ri.created_at, ri.updated_at
                    FROM return_items ri
                    WHERE ri.return_request_id = :return_request_id
                    ORDER BY ri.created_at
                """
                
                items_result = conn.execute(text(get_items_sql), {'return_request_id': return_row.id})
                items = []
                
                for item_row in items_result:
                    items.append({
                        'id': item_row.id,
                        'sku': item_row.sku,
                        'quantity_returned': item_row.quantity_returned,
                        'unit_price': float(item_row.unit_price),
                        'sale_price': float(item_row.sale_price),
                        'return_reason': item_row.return_reason,
                        'status': item_row.status,
                        'created_at': item_row.created_at.isoformat() if item_row.created_at else None,
                        'updated_at': item_row.updated_at.isoformat() if item_row.updated_at else None
                    })
                
                return_data['items'] = items
                return return_data
                
        except Exception as exc:
            logger.error(f"Error fetching return {return_id}: {str(exc)}")
            return None
    
    def get_returns_by_order_id(self, order_id: str) -> List[Dict]:
        """
        Get all returns for a specific order
        
        Args:
            order_id: The order ID to fetch returns for
            
        Returns:
            List of return dictionaries
        """
        try:
            with get_raw_transaction() as conn:
                get_returns_sql = """
                    SELECT 
                        rr.id, rr.return_id, rr.order_id, rr.customer_id,
                        rr.return_type, rr.return_reason, rr.return_method,
                        rr.status, rr.total_refund_amount, rr.refund_status,
                        rr.approved_at, rr.processed_at, rr.completed_at,
                        rr.created_at, rr.updated_at
                    FROM return_requests rr
                    JOIN orders o ON rr.order_id = o.order_id
                    WHERE o.order_id = :order_id
                    ORDER BY rr.created_at DESC
                """
                
                result = conn.execute(text(get_returns_sql), {'order_id': order_id})
                returns = []
                
                for return_row in result:
                    return_data = {
                        'id': return_row.id,
                        'return_id': return_row.return_id,
                        'order_id': order_id,
                        'customer_id': return_row.customer_id,
                        'return_type': return_row.return_type,
                        'return_reason': return_row.return_reason,
                        'return_method': return_row.return_method,
                        'status': return_row.status,
                        'total_refund_amount': float(return_row.total_refund_amount) if return_row.total_refund_amount else 0.0,
                        'refund_status': return_row.refund_status,
                        'approved_at': return_row.approved_at.isoformat() if return_row.approved_at else None,
                        'processed_at': return_row.processed_at.isoformat() if return_row.processed_at else None,
                        'completed_at': return_row.completed_at.isoformat() if return_row.completed_at else None,
                        'created_at': return_row.created_at.isoformat() if return_row.created_at else None,
                        'updated_at': return_row.updated_at.isoformat() if return_row.updated_at else None
                    }
                    returns.append(return_data)
                
                return returns
                
        except Exception as exc:
            logger.error(f"Error fetching returns for order {order_id}: {str(exc)}")
            return []
    
    def get_returns_by_customer_id(self, customer_id: str, limit: int = 50, offset: int = 0) -> List[Dict]:
        """
        Get all returns for a specific customer
        
        Args:
            customer_id: The customer ID to fetch returns for
            limit: Maximum number of returns to fetch
            offset: Number of returns to skip
            
        Returns:
            List of return dictionaries
        """
        try:
            with get_raw_transaction() as conn:
                get_returns_sql = """
                    SELECT 
                        rr.id, rr.return_id, rr.order_id, rr.customer_id,
                        rr.return_type, rr.return_reason, rr.return_method,
                        rr.status, rr.total_refund_amount, rr.refund_status,
                        rr.approved_at, rr.processed_at, rr.completed_at,
                        rr.created_at, rr.updated_at,
                        o.order_id as order_reference
                    FROM return_requests rr
                    JOIN orders o ON rr.order_id = o.order_id
                    WHERE rr.customer_id = :customer_id
                    ORDER BY rr.created_at DESC
                    LIMIT :limit OFFSET :offset
                """
                
                result = conn.execute(text(get_returns_sql), {
                    'customer_id': customer_id,
                    'limit': limit,
                    'offset': offset
                })
                returns = []
                
                for return_row in result:
                    return_data = {
                        'id': return_row.id,
                        'return_id': return_row.return_id,
                        'order_id': return_row.order_reference,
                        'customer_id': return_row.customer_id,
                        'return_type': return_row.return_type,
                        'return_reason': return_row.return_reason,
                        'return_method': return_row.return_method,
                        'status': return_row.status,
                        'total_refund_amount': float(return_row.total_refund_amount) if return_row.total_refund_amount else 0.0,
                        'refund_status': return_row.refund_status,
                        'approved_at': return_row.approved_at.isoformat() if return_row.approved_at else None,
                        'processed_at': return_row.processed_at.isoformat() if return_row.processed_at else None,
                        'completed_at': return_row.completed_at.isoformat() if return_row.completed_at else None,
                        'created_at': return_row.created_at.isoformat() if return_row.created_at else None,
                        'updated_at': return_row.updated_at.isoformat() if return_row.updated_at else None
                    }
                    returns.append(return_data)
                
                return returns
                
        except Exception as exc:
            logger.error(f"Error fetching returns for customer {customer_id}: {str(exc)}")
            return []
