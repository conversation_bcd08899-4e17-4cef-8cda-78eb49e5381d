"""
Return management API endpoints for api/v1 with token validation
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Query, Header
from app.core.token_validation_core import validate_token_and_return_items, validate_token_and_return_full_order
from app.services.return_service import ReturnService
from app.models.return_requests import (
    PartialReturnRequest, FullReturnRequest, ReturnProcessResponse,
    ReturnDetailsResponse, ReturnListResponse, ReturnResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()
return_service = ReturnService()


@router.post("/return-items", response_model=ReturnProcessResponse)
async def return_order_items_with_token(
    return_request: PartialReturnRequest,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Return specific items from an order with token validation
    
    Args:
        return_request: Request containing order_id and items to return
        authorization: Bearer token for authentication
        
    Returns:
        ReturnProcessResponse with return details
    """
    try:
        # Extract token from Authorization header
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
        
        token = authorization.replace("Bearer ", "")
        
        # Convert request to format expected by core function
        items_to_return = [
            {"sku": item.sku, "quantity": item.quantity}
            for item in return_request.items
        ]
        
        # Use token validation core function (assumes customer_id is extracted from token)
        # For now, we'll use a placeholder customer_id - this should be extracted from the token
        customer_id = "placeholder_customer_id"  # TODO: Extract from token
        
        result = await validate_token_and_return_items(
            token=token,
            customer_id=customer_id,
            order_id=return_request.order_id,
            items_to_return=items_to_return
        )
        
        return ReturnProcessResponse(
            success=result["success"],
            message=result["message"],
            order_id=result["order_id"],
            return_id=result["return_id"],
            return_type=result["return_type"],
            returned_items=result["returned_items"],
            order_status=result["order_status"],
            total_refund_amount=result["total_refund_amount"],
            
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error processing partial return for order {return_request.order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while processing return: {str(exc)}"
        )


@router.post("/return-full-order", response_model=ReturnProcessResponse)
async def return_full_order_with_token(
    return_request: FullReturnRequest,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Return all items in an order with token validation
    
    Args:
        return_request: Request containing order_id for full return
        authorization: Bearer token for authentication
        
    Returns:
        ReturnProcessResponse with return details
    """
    try:
        # Extract token from Authorization header
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
        
        token = authorization.replace("Bearer ", "")
        
        # Use token validation core function (assumes customer_id is extracted from token)
        # For now, we'll use a placeholder customer_id - this should be extracted from the token
        customer_id = "placeholder_customer_id"  # TODO: Extract from token
        
        result = await validate_token_and_return_full_order(
            token=token,
            customer_id=customer_id,
            order_id=return_request.order_id
        )
        
        return ReturnProcessResponse(
            success=result["success"],
            message=result["message"],
            order_id=result["order_id"],
            return_id=result["return_id"],
            return_type=result["return_type"],
            returned_items=result["returned_items"],
            order_status=result["order_status"],
            total_refund_amount=result["total_refund_amount"],
            
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error processing full return for order {return_request.order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while processing full return: {str(exc)}"
        )


@router.get("/return/{return_id}", response_model=ReturnDetailsResponse)
async def get_return_details_with_token(
    return_id: str,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Get details of a specific return by return_id with token validation
    
    Args:
        return_id: The return ID to fetch details for
        authorization: Bearer token for authentication
        
    Returns:
        ReturnDetailsResponse with return details
    """
    try:
        # Extract token from Authorization header
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
        
        token = authorization.replace("Bearer ", "")
        
        # TODO: Validate token and extract customer_id
        # For now, we'll fetch the return and validate ownership later
        
        return_data = return_service.get_return_by_id(return_id)
        
        if not return_data:
            return ReturnDetailsResponse(
                success=False,
                return_data=None,
                message=f"Return {return_id} not found"
            )
        
        # TODO: Validate that the return belongs to the authenticated customer
        
        return ReturnDetailsResponse(
            success=True,
            return_data=ReturnResponse(**return_data),
            message="Return details fetched successfully"
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error fetching return details for {return_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching return details: {str(exc)}"
        )


@router.get("/customer/returns", response_model=ReturnListResponse)
async def get_customer_returns_with_token(
    authorization: str = Header(..., description="Bearer token for authentication"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of returns to fetch"),
    offset: int = Query(0, ge=0, description="Number of returns to skip")
):
    """
    Get all returns for the authenticated customer
    
    Args:
        authorization: Bearer token for authentication
        limit: Maximum number of returns to fetch (1-100)
        offset: Number of returns to skip for pagination
        
    Returns:
        ReturnListResponse with list of returns
    """
    try:
        # Extract token from Authorization header
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
        
        token = authorization.replace("Bearer ", "")
        
        # TODO: Validate token and extract customer_id
        customer_id = "placeholder_customer_id"  # TODO: Extract from token
        
        returns_data = return_service.get_returns_by_customer_id(customer_id, limit, offset)
        
        returns = [ReturnResponse(**return_data) for return_data in returns_data]
        
        return ReturnListResponse(
            success=True,
            returns=returns,
            total_count=len(returns)
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error fetching customer returns: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching returns: {str(exc)}"
        )


@router.get("/order/{order_id}/returns", response_model=ReturnListResponse)
async def get_order_returns_with_token(
    order_id: str,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Get all returns for a specific order with token validation
    
    Args:
        order_id: The order ID to fetch returns for
        authorization: Bearer token for authentication
        
    Returns:
        ReturnListResponse with list of returns
    """
    try:
        # Extract token from Authorization header
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header format")
        
        token = authorization.replace("Bearer ", "")
        
        # TODO: Validate token and verify customer owns the order
        
        returns_data = return_service.get_returns_by_order_id(order_id)
        
        returns = [ReturnResponse(**return_data) for return_data in returns_data]
        
        return ReturnListResponse(
            success=True,
            returns=returns,
            total_count=len(returns)
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error fetching returns for order {order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching returns: {str(exc)}"
        )
