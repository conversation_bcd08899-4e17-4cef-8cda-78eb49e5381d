"""
Return management API endpoints for app/v1
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, Query
from app.core.order_return import return_order_items_core, return_full_order_core
from app.services.return_service import ReturnService
from app.models.return_requests import (
    PartialReturnRequest, FullReturnRequest, ReturnProcessResponse,
    ReturnDetailsResponse, ReturnListResponse, ReturnResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()
return_service = ReturnService()


@router.post("/return-items", response_model=ReturnProcessResponse)
async def return_order_items(return_request: PartialReturnRequest):
    """
    Return specific items from an order
    
    Args:
        return_request: Request containing order_id and items to return
        
    Returns:
        ReturnProcessResponse with return details
    """
    try:
        # Convert request to format expected by core function
        items_to_return = [
            {"sku": item.sku, "quantity": item.quantity}
            for item in return_request.items
        ]
        
        result = await return_order_items_core(return_request.order_id, items_to_return)
        
        return ReturnProcessResponse(
            success=result["success"],
            message=result["message"],
            order_id=result["order_id"],
            return_id=result["return_id"],
            return_type=result["return_type"],
            returned_items=result["returned_items"],
            order_status=result["order_status"],
            total_refund_amount=result["total_refund_amount"],
            
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error processing partial return for order {return_request.order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while processing return: {str(exc)}"
        )


@router.post("/return-full-order", response_model=ReturnProcessResponse)
async def return_full_order(return_request: FullReturnRequest):
    """
    Return all items in an order
    
    Args:
        return_request: Request containing order_id for full return
        
    Returns:
        ReturnProcessResponse with return details
    """
    try:
        result = await return_full_order_core(return_request.order_id)
        
        return ReturnProcessResponse(
            success=result["success"],
            message=result["message"],
            order_id=result["order_id"],
            return_id=result["return_id"],
            return_type=result["return_type"],
            returned_items=result["returned_items"],
            order_status=result["order_status"],
            total_refund_amount=result["total_refund_amount"],
            
        )
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error processing full return for order {return_request.order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while processing full return: {str(exc)}"
        )


@router.get("/return/{return_id}", response_model=ReturnDetailsResponse)
async def get_return_details(return_id: str):
    """
    Get details of a specific return by return_id
    
    Args:
        return_id: The return ID to fetch details for
        
    Returns:
        ReturnDetailsResponse with return details
    """
    try:
        return_data = return_service.get_return_by_id(return_id)
        
        if not return_data:
            return ReturnDetailsResponse(
                success=False,
                return_data=None,
                message=f"Return {return_id} not found"
            )
        
        return ReturnDetailsResponse(
            success=True,
            return_data=ReturnResponse(**return_data),
            message="Return details fetched successfully"
        )
        
    except Exception as exc:
        logger.error(f"Error fetching return details for {return_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching return details: {str(exc)}"
        )


@router.get("/order/{order_id}/returns", response_model=ReturnListResponse)
async def get_returns_by_order(order_id: str):
    """
    Get all returns for a specific order
    
    Args:
        order_id: The order ID to fetch returns for
        
    Returns:
        ReturnListResponse with list of returns
    """
    try:
        returns_data = return_service.get_returns_by_order_id(order_id)
        
        returns = [ReturnResponse(**return_data) for return_data in returns_data]
        
        return ReturnListResponse(
            success=True,
            returns=returns,
            total_count=len(returns)
        )
        
    except Exception as exc:
        logger.error(f"Error fetching returns for order {order_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching returns: {str(exc)}"
        )


@router.get("/customer/{customer_id}/returns", response_model=ReturnListResponse)
async def get_returns_by_customer(
    customer_id: str,
    limit: int = Query(50, ge=1, le=100, description="Maximum number of returns to fetch"),
    offset: int = Query(0, ge=0, description="Number of returns to skip")
):
    """
    Get all returns for a specific customer
    
    Args:
        customer_id: The customer ID to fetch returns for
        limit: Maximum number of returns to fetch (1-100)
        offset: Number of returns to skip for pagination
        
    Returns:
        ReturnListResponse with list of returns
    """
    try:
        returns_data = return_service.get_returns_by_customer_id(customer_id, limit, offset)
        
        returns = [ReturnResponse(**return_data) for return_data in returns_data]
        
        return ReturnListResponse(
            success=True,
            returns=returns,
            total_count=len(returns)
        )
        
    except Exception as exc:
        logger.error(f"Error fetching returns for customer {customer_id}: {str(exc)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while fetching returns: {str(exc)}"
        )
