"""
Core functions for order return operations
"""

import logging
import requests
import threading
from datetime import datetime, timedelta, timezone
from typing import List, Dict
from fastapi import HTT<PERSON>Exception
from sqlalchemy import text
from app.connections.database import get_raw_transaction
from app.core.constants import OrderStatus
from app.utils.return_utils import calculate_return_refund_amount

logger = logging.getLogger(__name__)


def validate_item_return_eligibility(item_row, sku: str) -> Dict:
    """
    Validate if an item is eligible for return based on business rules.
    
    Args:
        item_row: Database row containing item details
        sku: SKU being validated
        
    Returns:
        Dict with validation result and details
    """
    validation_errors = []
    
    # Check is_returnable
    if not item_row.is_returnable:
        validation_errors.append(f"SKU {sku} is not returnable (is_returnable=False)")
    
    # Check return_type (10 = only return, 11 = return and exchange)
    if item_row.return_type not in ['10', '11']:
        validation_errors.append(f"SKU {sku} return type '{item_row.return_type}' does not allow returns (must be '10' or '11')")
    
    # Check item status (25=WMS_PICKED, 26=WMS_FULFILLED, 27=WMS_INVOICED, 31=TMS_SYNCED)
    allowed_item_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
    if item_row.status not in allowed_item_statuses:
        validation_errors.append(f"SKU {sku} status {item_row.status} does not allow returns (must be in {allowed_item_statuses})")
    
    # Check return window
    if item_row.return_window > 0:  # Only check if return window is specified
        item_updated_date = item_row.updated_at
        
        # Handle timezone-aware datetime from database
        if isinstance(item_updated_date, str):
            item_updated_date = datetime.fromisoformat(item_updated_date.replace('Z', '+00:00'))
        
        # Ensure we have timezone-aware datetime for comparison
        if item_updated_date.tzinfo is None:
            item_updated_date = item_updated_date.replace(tzinfo=timezone.utc)
        
        # Calculate return deadline (updated_at + return_window days)
        return_deadline = item_updated_date + timedelta(days=item_row.return_window)
        current_time = datetime.now(timezone.utc)
        
        # Allow return if current time is BEFORE or equal to deadline
        if current_time > return_deadline:
            validation_errors.append(f"SKU {sku} return window expired (deadline was {return_deadline.strftime('%Y-%m-%d %H:%M:%S UTC')}, current time: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')})")
    
    return {
        'is_eligible': len(validation_errors) == 0,
        'errors': validation_errors,
        'item_details': {
            'sku': sku,
            'is_returnable': item_row.is_returnable,
            'return_type': item_row.return_type,
            'return_window': item_row.return_window,
            'status': item_row.status,
            'updated_at': item_row.updated_at
        }
    }


async def return_order_items_core(order_id: str, items_to_return: List[Dict]):
    """
    Return specific items from an order.
    
    Args:
        order_id: The order ID to return items from
        items_to_return: List of items with sku and quantity to return
    """
    try:
        with get_raw_transaction() as conn:
            # Check if order exists and get current status
            check_order_sql = """
                SELECT id, order_id, status, facility_name, customer_id 
                FROM orders 
                WHERE order_id = :order_id
            """
            
            result = conn.execute(text(check_order_sql), {'order_id': order_id})
            order_row = result.fetchone()
            
            if not order_row:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Order {order_id} not found"
                )
            
            current_status = order_row.status
            facility_name = order_row.facility_name
            
            # Check if order status allows returns (25, 26, 27, 31)
            allowed_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
            
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order {order_id} cannot be returned. Current status: {current_status} is not in allowed statuses {allowed_statuses}"
                )
            
            # Get current order items with return eligibility fields
            get_items_sql = """
                SELECT id, sku, quantity, status, unit_price, sale_price, 
                       is_returnable, return_type, return_window, updated_at
                FROM order_items 
                WHERE order_id = :order_pk
            """
            
            items_result = conn.execute(text(get_items_sql), {'order_pk': order_row.id})
            current_items = {row.sku: row for row in items_result.fetchall()}
            
            # Validate items to return
            returned_items = []
            validation_errors = []
            
            for item in items_to_return:
                sku = item['sku']
                return_quantity = item['quantity']
                
                if sku not in current_items:
                    raise HTTPException(
                        status_code=400,
                        detail=f"SKU {sku} not found in order {order_id}"
                    )
                
                current_item = current_items[sku]
                if return_quantity > current_item.quantity:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot return {return_quantity} units of {sku}. Order only contains {current_item.quantity} units"
                    )
                
                # Validate return eligibility
                eligibility_result = validate_item_return_eligibility(current_item, sku)
                if not eligibility_result['is_eligible']:
                    validation_errors.extend(eligibility_result['errors'])
            
            # If any items are not eligible for return, raise error
            if validation_errors:
                raise HTTPException(
                    status_code=400,
                    detail=f"Return not allowed: {'; '.join(validation_errors)}"
                )
            
            temp_return_items = []
            for item in items_to_return:
                sku = item['sku']
                return_quantity = item['quantity']
                current_item = current_items[sku]
                temp_return_items.append({
                    'quantity_returned': return_quantity,
                    'sale_price': float(current_item.sale_price)
                })
            
            total_refund_amount = calculate_return_refund_amount(temp_return_items)
            
            create_return_request_sql = """
                INSERT INTO return_requests (
                    return_id, order_id, customer_id, return_type, return_reason,
                    return_method, status, total_refund_amount, refund_status,
                    approved_at, created_at, updated_at
                ) VALUES (
                    CONCAT((SELECT order_id FROM orders WHERE id = :order_pk), NEXTVAL('return_requests_id_seq')),
                    :order_pk, :customer_id, :return_type, :return_reason,
                    :return_method, :status, :total_refund_amount, :refund_status,
                    NOW(), NOW(), NOW()
                )
                RETURNING id, return_id
            """
            
            return_request_result = conn.execute(text(create_return_request_sql), {
                'order_pk': order_row.id,
                'customer_id': order_row.customer_id,
                'return_type': 'partial',
                'return_reason': 'Customer requested return',
                'return_method': 'api',
                'status': 'approved',
                'total_refund_amount': total_refund_amount,
                'refund_status': 'pending'
            })
            
            result_row = return_request_result.fetchone()
            return_request_id = result_row.id
            return_id = result_row.return_id
            
            logger.info(f"Created return request with ID: {return_request_id}, return_id: {return_id}")

            # Process the return for eligible items
            for item in items_to_return:
                sku = item['sku']
                return_quantity = item['quantity']
                current_item = current_items[sku]
                
                # Create return item record
                create_return_item_sql = """
                    INSERT INTO return_items (
                        return_request_id, order_item_id, sku, quantity_returned,
                        unit_price, sale_price, return_reason, status,
                        created_at, updated_at
                    ) VALUES (
                        :return_request_id, :order_item_id, :sku, :quantity_returned,
                        :unit_price, :sale_price, :return_reason, :status,
                        NOW(), NOW()
                    )
                """
                
                conn.execute(text(create_return_item_sql), {
                    'return_request_id': return_request_id,
                    'order_item_id': current_item.id,
                    'sku': sku,
                    'quantity_returned': return_quantity,
                    'unit_price': float(current_item.unit_price),
                    'sale_price': float(current_item.sale_price),
                    'return_reason': 'Customer requested return',
                    'status': 'approved'
                })
                
                # Update item status to RETURN
                update_item_sql = """
                    UPDATE order_items 
                    SET status = :status, updated_at = NOW()
                    WHERE id = :item_id
                """
                
                conn.execute(text(update_item_sql), {
                    'status': OrderStatus.RETURN,
                    'item_id': current_item.id
                })
                
                returned_items.append({
                    'sku': sku,
                    'quantity': return_quantity,
                    'unit_price': float(current_item.unit_price),
                    'sale_price': float(current_item.sale_price),
                    'status': OrderStatus.RETURN
                })
            
            # Check if all items are returned (full return) or partial return
            remaining_items_sql = """
                SELECT COUNT(*) as count
                FROM order_items 
                WHERE order_id = :order_pk AND status != :return_status
            """
            
            remaining_result = conn.execute(text(remaining_items_sql), {
                'order_pk': order_row.id,
                'return_status': OrderStatus.RETURN
            })
            remaining_count = remaining_result.fetchone().count
            
            # Update order status based on return type
            if remaining_count == 0:
                # Full return - set order status to RETURN
                new_order_status = OrderStatus.RETURN
                return_type = "full"
            else:
                # Partial return - set order status to PARTIALLY_FULFILLED
                new_order_status = OrderStatus.PARTIALLY_FULFILLED
                return_type = "partial"
            
            # Update return request with correct return type
            update_return_request_sql = """
                UPDATE return_requests 
                SET return_type = :return_type, updated_at = NOW()
                WHERE id = :return_request_id
            """
            
            conn.execute(text(update_return_request_sql), {
                'return_type': return_type,
                'return_request_id': return_request_id
            })
            
            update_order_sql = """
                UPDATE orders 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            """
            
            conn.execute(text(update_order_sql), {
                'status': new_order_status,
                'order_id': order_id
            })
            
            conn.commit()
            logger.info(f"Order {order_id} items returned successfully. Return type: {return_type}")
        
        try:
            _trigger_potions_async(order_id)
            logger.info(f"Successfully triggered async Potions call for order {order_id}")
        except Exception as e:
            logger.error(f"Failed to trigger Potions for order {order_id}: {str(e)}")
        
        return {
            "success": True,
            "message": f"Items returned successfully from order {order_id}",
            "order_id": order_id,
            "return_id": return_id,
            "return_type": return_type,
            "returned_items": returned_items,
            "order_status": new_order_status,
            "total_refund_amount": total_refund_amount
        }
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error returning items from order {order_id}: {exc}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error while processing return: {str(exc)}"
        ) from exc


async def return_full_order_core(order_id: str):
    """
    Return all items in an order.
    
    Args:
        order_id: The order ID to return completely
    """
    try:
        with get_raw_transaction() as conn:
            # Check if order exists and get current status
            check_order_sql = """
                SELECT id, order_id, status, facility_name, customer_id 
                FROM orders 
                WHERE order_id = :order_id
            """
            
            result = conn.execute(text(check_order_sql), {'order_id': order_id})
            order_row = result.fetchone()
            
            if not order_row:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Order {order_id} not found"
                )
            
            current_status = order_row.status
            facility_name = order_row.facility_name
            
            # Check if order status allows returns (25, 26, 27, 31)
            allowed_statuses = [OrderStatus.WMS_PICKED, OrderStatus.WMS_FULFILLED, OrderStatus.WMS_INVOICED, 31]
            
            if current_status not in allowed_statuses:
                raise HTTPException(
                    status_code=400,
                    detail=f"Order {order_id} cannot be returned. Current status: {current_status} is not in allowed statuses {allowed_statuses}"
                )
            
            if current_status == OrderStatus.RETURN:
                return {
                    "success": True,
                    "message": f"Order {order_id} is already returned",
                    "order_id": order_id,
                    "return_type": "full",
                    "returned_items": [],
                    "order_status": OrderStatus.RETURN
                }
            
            # Get all order items with return eligibility fields
            get_items_sql = """
                SELECT id, sku, quantity, status, unit_price, sale_price,
                       is_returnable, return_type, return_window, updated_at
                FROM order_items 
                WHERE order_id = :order_pk
            """
            
            items_result = conn.execute(text(get_items_sql), {'order_pk': order_row.id})
            all_items = items_result.fetchall()
            
            # Validate ALL items for return eligibility (full order return requirement)
            validation_errors = []
            for item in all_items:
                eligibility_result = validate_item_return_eligibility(item, item.sku)
                if not eligibility_result['is_eligible']:
                    validation_errors.extend(eligibility_result['errors'])
            
            # If ANY item is not eligible for return, reject the full order return
            if validation_errors:
                raise HTTPException(
                    status_code=400,
                    detail=f"Full order return not allowed: {'; '.join(validation_errors)}"
                )
            
            returned_items = []
            
            temp_return_items = []
            for item in all_items:
                temp_return_items.append({
                    'quantity_returned': item.quantity,
                    'sale_price': float(item.sale_price)
                })
            
            total_refund_amount = calculate_return_refund_amount(temp_return_items)
            
            create_return_request_sql = """
                INSERT INTO return_requests (
                    return_id, order_id, customer_id, return_type, return_reason,
                    return_method, status, total_refund_amount, refund_status,
                    approved_at, created_at, updated_at
                ) VALUES (
                    CONCAT((SELECT order_id FROM orders WHERE id = :order_pk), NEXTVAL('return_requests_id_seq')),
                    :order_pk, :customer_id, :return_type, :return_reason,
                    :return_method, :status, :total_refund_amount, :refund_status,
                    NOW(), NOW(), NOW()
                )
                RETURNING id, return_id
            """
            
            return_request_result = conn.execute(text(create_return_request_sql), {
                'order_pk': order_row.id,
                'customer_id': order_row.customer_id,
                'return_type': 'full',
                'return_reason': 'Customer requested full return',
                'return_method': 'api',
                'status': 'approved',
                'total_refund_amount': total_refund_amount,
                'refund_status': 'pending'
            })
            
            result_row = return_request_result.fetchone()
            return_request_id = result_row.id
            return_id = result_row.return_id
            
            logger.info(f"Created full return request with ID: {return_request_id}, return_id: {return_id}")

            # Process full return for all eligible items
            returned_items = []
            
            for item in all_items:
                # Create return item record
                create_return_item_sql = """
                    INSERT INTO return_items (
                        return_request_id, order_item_id, sku, quantity_returned,
                        unit_price, sale_price, return_reason, status,
                        created_at, updated_at
                    ) VALUES (
                        :return_request_id, :order_item_id, :sku, :quantity_returned,
                        :unit_price, :sale_price, :return_reason, :status,
                        NOW(), NOW()
                    )
                """
                
                conn.execute(text(create_return_item_sql), {
                    'return_request_id': return_request_id,
                    'order_item_id': item.id,
                    'sku': item.sku,
                    'quantity_returned': item.quantity,
                    'unit_price': float(item.unit_price),
                    'sale_price': float(item.sale_price),
                    'return_reason': 'Customer requested full return',
                    'status': 'approved'
                })
                
                # Update item status to RETURN
                update_item_sql = """
                    UPDATE order_items 
                    SET status = :status, updated_at = NOW()
                    WHERE id = :item_id
                """
                
                conn.execute(text(update_item_sql), {
                    'status': OrderStatus.RETURN,
                    'item_id': item.id
                })
                
                returned_items.append({
                    'sku': item.sku,
                    'quantity': item.quantity,
                    'unit_price': float(item.unit_price),
                    'sale_price': float(item.sale_price),
                    'status': OrderStatus.RETURN
                })
            
            # Update order status to RETURN
            update_order_sql = """
                UPDATE orders 
                SET status = :status, updated_at = NOW()
                WHERE order_id = :order_id
            """
            
            conn.execute(text(update_order_sql), {
                'status': OrderStatus.RETURN,
                'order_id': order_id
            })
            
            conn.commit()
            logger.info(f"Full order {order_id} returned successfully")
        
        try:
            _trigger_potions_async(order_id)
            logger.info(f"Successfully triggered async Potions call for order {order_id}")
        except Exception as e:
            logger.error(f"Failed to trigger Potions for order {order_id}: {str(e)}")
        
        return {
            "success": True,
            "message": f"Full order {order_id} returned successfully",
            "order_id": order_id,
            "return_id": return_id,
            "return_type": "full",
            "returned_items": returned_items,
            "order_status": OrderStatus.RETURN,
            "total_refund_amount": total_refund_amount
        }
        
    except HTTPException:
        raise
    except Exception as exc:
        logger.error(f"Error returning full order {order_id}: {exc}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error while processing full return: {str(exc)}"
        ) from exc


def _async_call_potions(order_id: str):
    try:
        from app.integrations.potions_config import get_potions_config
        
        config = get_potions_config()
        if not config or not config.integration_enabled:
            logger.warning(f"Potions integration disabled, skipping return processing for order {order_id}")
            return
        
        base_url = config.potions_base_url.replace("host.docker.internal", "localhost")
        potions_url = f"{base_url}/api/potions/integrations/return/order/"
        
        params = {
            "token": "oh61wNe31XocXWxvrRHLT0eGVQBOnI"
        }
        
        payload = {
            "order_id": order_id
        }
        
        response = requests.post(
            potions_url,
            json=payload,
            params=params,
            timeout=config.timeout
        )
        
        if response.status_code == 200:
            logger.info(f"Successfully triggered Potions return processing for order {order_id}")
        else:
            logger.error(f"Potions return processing failed for order {order_id}: {response.status_code} - {response.text}")
            
    except Exception as e:
        logger.error(f"Error calling Potions for order {order_id}: {str(e)}")


def _trigger_potions_async(order_id: str):
    thread = threading.Thread(target=_async_call_potions, args=(order_id,))
    thread.daemon = True
    thread.start()
    logger.info(f"Started async Potions call for order {order_id}")
